import React, { useRef, useState, useEffect, Suspense } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Text3D, Center, RenderTexture, PerspectiveCamera, useTexture } from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';
import { QRCodeSVG } from 'qrcode.react';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

interface Interactive3DCardProps {
  profileData: {
    full_name: string;
    avatar_url: string;
    date_of_birth: string;
  };
  medicalData: {
    blood_group: string;
  };
  className?: string;
}

// Custom shader material for ray-marching effects
const rayMarchingVertexShader = `
  varying vec2 vUv;
  varying vec3 vPosition;
  varying vec3 vNormal;
  
  void main() {
    vUv = uv;
    vPosition = position;
    vNormal = normal;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`;

const rayMarchingFragmentShader = `
  uniform float time;
  uniform vec3 color;
  uniform float metalness;
  uniform float roughness;
  uniform float clearcoat;
  uniform float iridescence;
  
  varying vec2 vUv;
  varying vec3 vPosition;
  varying vec3 vNormal;
  
  // Ray marching distance function for fluid surface
  float sdSphere(vec3 p, float r) {
    return length(p) - r;
  }
  
  float sdBox(vec3 p, vec3 b) {
    vec3 q = abs(p) - b;
    return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0);
  }
  
  float opSmoothUnion(float d1, float d2, float k) {
    float h = clamp(0.5 + 0.5 * (d2 - d1) / k, 0.0, 1.0);
    return mix(d2, d1, h) - k * h * (1.0 - h);
  }
  
  float map(vec3 p) {
    // Animated fluid surface
    vec3 q = p;
    q.x += sin(time * 0.5 + p.y * 2.0) * 0.1;
    q.z += cos(time * 0.3 + p.x * 1.5) * 0.1;
    
    float sphere1 = sdSphere(q - vec3(0.0, sin(time * 0.4) * 0.2, 0.0), 0.8);
    float sphere2 = sdSphere(q - vec3(sin(time * 0.6) * 0.3, 0.0, cos(time * 0.5) * 0.3), 0.6);
    float box = sdBox(q, vec3(0.7, 0.1, 0.7));
    
    return opSmoothUnion(opSmoothUnion(sphere1, sphere2, 0.3), box, 0.2);
  }
  
  vec3 calcNormal(vec3 p) {
    const float eps = 0.001;
    const vec2 h = vec2(eps, 0);
    return normalize(vec3(
      map(p + h.xyy) - map(p - h.xyy),
      map(p + h.yxy) - map(p - h.yxy),
      map(p + h.yyx) - map(p - h.yyx)
    ));
  }
  
  void main() {
    vec2 uv = vUv * 2.0 - 1.0;
    
    // Ray marching setup
    vec3 ro = vec3(0.0, 0.0, 2.0);
    vec3 rd = normalize(vec3(uv, -1.0));
    
    float t = 0.0;
    for (int i = 0; i < 64; i++) {
      vec3 p = ro + rd * t;
      float d = map(p);
      if (d < 0.001) break;
      t += d;
      if (t > 10.0) break;
    }
    
    vec3 p = ro + rd * t;
    vec3 normal = calcNormal(p);
    
    // Lighting
    vec3 lightPos = vec3(2.0, 2.0, 2.0);
    vec3 lightDir = normalize(lightPos - p);
    float diff = max(dot(normal, lightDir), 0.0);
    
    // Fresnel effect for iridescence
    float fresnel = pow(1.0 - max(dot(normal, -rd), 0.0), 2.0);
    
    // Iridescent color shift
    vec3 iridColor = vec3(
      sin(fresnel * 6.28 + time) * 0.5 + 0.5,
      sin(fresnel * 6.28 + time + 2.09) * 0.5 + 0.5,
      sin(fresnel * 6.28 + time + 4.18) * 0.5 + 0.5
    );
    
    vec3 finalColor = mix(color, iridColor, iridescence * fresnel);
    finalColor *= diff * 0.8 + 0.2;
    
    // Add clearcoat effect
    float clearcoatFresnel = pow(1.0 - max(dot(normal, -rd), 0.0), 5.0);
    finalColor = mix(finalColor, vec3(1.0), clearcoat * clearcoatFresnel * 0.3);
    
    gl_FragColor = vec4(finalColor, 0.9);
  }
`;

// 3D Card Mesh Component
function Card3DMesh({ profileData, medicalData }: { profileData: any; medicalData: any }) {
  const meshRef = useRef<THREE.Mesh>(null);
  const materialRef = useRef<THREE.ShaderMaterial>(null);
  const { mouse, viewport } = useThree();
  
  useFrame((state) => {
    if (materialRef.current) {
      materialRef.current.uniforms.time.value = state.clock.elapsedTime;
    }
    
    if (meshRef.current) {
      // Subtle rotation based on mouse position
      meshRef.current.rotation.x = mouse.y * 0.1;
      meshRef.current.rotation.y = mouse.x * 0.1;
      
      // Floating animation
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  const shaderMaterial = new THREE.ShaderMaterial({
    vertexShader: rayMarchingVertexShader,
    fragmentShader: rayMarchingFragmentShader,
    uniforms: {
      time: { value: 0 },
      color: { value: new THREE.Color(0x1a1a2e) },
      metalness: { value: 0.5 },
      roughness: { value: 0.15 },
      clearcoat: { value: 1.0 },
      iridescence: { value: 0.8 }
    },
    transparent: true,
    side: THREE.DoubleSide
  });

  return (
    <mesh ref={meshRef} material={shaderMaterial}>
      <boxGeometry args={[3, 2, 0.1]} />
      <shaderMaterial ref={materialRef} attach="material" {...shaderMaterial} />
    </mesh>
  );
}

// UI Overlay Component for rendering text and QR code
function UIOverlay({ profileData, medicalData }: { profileData: any; medicalData: any }) {
  const { user } = useAuth();
  
  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age.toString();
  };

  // Generate QR code data (non-sensitive information only)
  const qrData = JSON.stringify({
    name: profileData.full_name || 'User',
    id: user?.id.slice(0, 8) || 'unknown',
    bloodGroup: medicalData.blood_group || 'Unknown',
    age: calculateAge(profileData.date_of_birth),
    type: 'CareAI_Patient_Card'
  });

  return (
    <div className="absolute inset-0 pointer-events-none">
      {/* Top section - Logo and Name */}
      <div className="absolute top-4 left-4 right-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <img 
            src="/images/UKUQALA.svg" 
            alt="Ukuqala Logo" 
            className="w-8 h-8 filter brightness-0 invert"
          />
          <div>
            <h3 className="text-white font-bold text-lg leading-tight">
              {profileData.full_name || 'User'}
            </h3>
            <p className="text-white/70 text-xs">CareAI Patient</p>
          </div>
        </div>
        
        {/* Profile Image */}
        <div className="w-12 h-12 rounded-full border-2 border-white/30 overflow-hidden">
          <img 
            src={profileData.avatar_url || '/images/default_user.jpg'} 
            alt="Profile" 
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* Middle section - Key Info */}
      <div className="absolute top-1/2 left-4 right-4 transform -translate-y-1/2">
        <div className="grid grid-cols-2 gap-4 text-white">
          <div>
            <p className="text-xs text-white/60 uppercase tracking-wide">Age</p>
            <p className="text-lg font-bold">{calculateAge(profileData.date_of_birth)}</p>
          </div>
          <div>
            <p className="text-xs text-white/60 uppercase tracking-wide">Blood Group</p>
            <p className="text-lg font-bold">{medicalData.blood_group || 'Unknown'}</p>
          </div>
        </div>
      </div>

      {/* Bottom section - QR Code and ID */}
      <div className="absolute bottom-4 left-4 right-4 flex items-end justify-between">
        <div>
          <p className="text-xs text-white/60 uppercase tracking-wide">Patient ID</p>
          <p className="text-sm font-mono text-white">#{user?.id.slice(0, 8)}</p>
        </div>
        
        {/* QR Code */}
        <div className="bg-white p-2 rounded">
          <QRCodeSVG
            value={qrData}
            size={40}
            level="M"
            includeMargin={false}
          />
        </div>
      </div>
    </div>
  );
}

// Text overlay using RenderTexture for 3D surface mapping
function TextOverlay({ profileData, medicalData }: { profileData: any; medicalData: any }) {
  return (
    <RenderTexture attach="map" width={512} height={512}>
      <PerspectiveCamera makeDefault position={[0, 0, 5]} />
      <color attach="background" args={['transparent']} />
      <UIOverlay profileData={profileData} medicalData={medicalData} />
    </RenderTexture>
  );
}

// Loading fallback component
function CardFallback() {
  return (
    <div className="w-full h-64 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl flex items-center justify-center">
      <div className="text-white text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
        <p className="text-sm">Loading 3D Card...</p>
      </div>
    </div>
  );
}

// Main Interactive 3D Card Component
const Interactive3DCard: React.FC<Interactive3DCardProps> = ({
  profileData,
  medicalData,
  className = ""
}) => {
  const { darkMode } = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className={`relative w-full h-64 rounded-xl overflow-hidden shadow-2xl ${className}`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ scale: 1.02 }}
    >
      {/* 3D Canvas */}
      <div className="absolute inset-0">
        <Suspense fallback={<CardFallback />}>
          <Canvas
            camera={{ position: [0, 0, 5], fov: 45 }}
            style={{ background: 'transparent' }}
            gl={{ alpha: true, antialias: true }}
          >
            <ambientLight intensity={0.3} />
            <pointLight position={[10, 10, 10]} intensity={0.8} />
            <pointLight position={[-10, -10, -10]} intensity={0.3} color="#4f46e5" />

            <Card3DMesh profileData={profileData} medicalData={medicalData} />

            {/* Environment lighting for better material rendering */}
            <hemisphereLight
              skyColor="#ffffff"
              groundColor="#444444"
              intensity={0.4}
            />
          </Canvas>
        </Suspense>
      </div>

      {/* UI Overlay */}
      <UIOverlay profileData={profileData} medicalData={medicalData} />

      {/* Hover effects */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: isHovered ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      />

      {/* Scan line effect */}
      <motion.div
        className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-blue-400 to-transparent"
        initial={{ y: 0, opacity: 0 }}
        animate={{
          y: isHovered ? 256 : 0,
          opacity: isHovered ? [0, 1, 1, 0] : 0
        }}
        transition={{
          duration: 2,
          repeat: isHovered ? Infinity : 0,
          ease: "linear"
        }}
      />

      {/* Corner accents */}
      <div className="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-white/30"></div>
      <div className="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-white/30"></div>
      <div className="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-white/30"></div>
      <div className="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-white/30"></div>
    </motion.div>
  );
};

export default Interactive3DCard;
